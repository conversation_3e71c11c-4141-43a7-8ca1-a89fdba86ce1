import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  Dimensions,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import MainCircle from './src/components/MainCircle';
import useAudioRecorder from './src/components/AudioRecorder';
import useAudioPlayer from './src/components/AudioPlayer';
import WhisperCard from './src/components/WhisperCard';
import FloatingMenu from './src/components/FloatingMenu';
import useWhispers from './src/hooks/useWhispers';
import { colors, fonts, spacing } from './src/styles/theme';

const { width, height } = Dimensions.get('window');

export default function App() {
  console.log('🚀 App.js: Component initializing...');

  const [currentMode, setCurrentMode] = useState('listen');
  const [currentWhisper, setCurrentWhisper] = useState(null);
  const [browseWhispers, setBrowseWhispers] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const {
    userId,
    isLoading,
    error,
    uploadWhisper,
    getRandomWhisperForUser,
    getBrowseWhispers,
    handleLikeWhisper,
    handleReportWhisper,
    clearError,
  } = useWhispers();

  const handleRecordingComplete = async (audioUri) => {
    console.log('📱 Recording completed, URI:', audioUri);

    if (!audioUri) {
      console.log('❌ No audio URI provided');
      return;
    }

    try {
      console.log('📤 Starting whisper upload...');
      const whisperId = await uploadWhisper(audioUri);

      if (whisperId) {
        console.log('✅ Whisper uploaded successfully with ID:', whisperId);
        Alert.alert('Úspech', 'Váš šepot bol zdieľaný! 👻');
        setCurrentMode('listen');
      } else {
        console.log('❌ Upload failed - no whisper ID returned');
        Alert.alert('Chyba', 'Nepodarilo sa nahrať šepot');
      }
    } catch (error) {
      console.error('❌ Error in handleRecordingComplete:', error);
      Alert.alert('Chyba', 'Nepodarilo sa nahrať šepot');
    }
  };

  console.log('🎙️ App: Initializing useAudioRecorder with handleRecordingComplete:', !!handleRecordingComplete);

  const audioRecorder = useAudioRecorder({
    onRecordingStart: () => setIsRecording(true),
    onRecordingStop: () => setIsRecording(false),
    onRecordingComplete: handleRecordingComplete,
  });

  console.log('🎙️ App: audioRecorder initialized:', !!audioRecorder);

  const audioPlayer = useAudioPlayer({
    audioUrl: currentWhisper?.audioUrl,
    onPlaybackStart: () => setIsPlaying(true),
    onPlaybackEnd: () => setIsPlaying(false),
  });

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [{ text: 'OK', onPress: clearError }]);
    }
  }, [error]);

  useEffect(() => {
    if (currentMode === 'listen') {
      loadRandomWhisper();
    } else if (currentMode === 'browse') {
      loadBrowseWhispers();
    }
  }, [currentMode]);

  const loadRandomWhisper = async () => {
    console.log('🎧 App: Loading random whisper...');
    try {
      const whisper = await getRandomWhisperForUser();
      console.log('🎧 App: Random whisper loaded:', whisper);
      setCurrentWhisper(whisper);

      if (!whisper) {
        console.log('⚠️ App: No whisper returned, showing alert');
        Alert.alert(
          'Žiadne šepoty',
          'Zatiaľ nie sú dostupné žiadne šepoty. Skúste najprv nahrať jeden! 🎙️',
          [{ text: 'OK', onPress: () => console.log('Alert dismissed') }]
        );
      } else {
        console.log('✅ App: Whisper set successfully:', whisper.id);
      }
    } catch (error) {
      console.error('❌ App: Error loading random whisper:', error);
      Alert.alert(
        'Chyba',
        'Nepodarilo sa načítať šepot. Skúste to znovu.',
        [{ text: 'OK', onPress: () => console.log('Error alert dismissed') }]
      );
    }
  };

  const loadBrowseWhispers = async () => {
    const whispers = await getBrowseWhispers();
    setBrowseWhispers(whispers);
  };

  const handleMainCirclePress = () => {
    switch (currentMode) {
      case 'listen':
        if (currentWhisper) {
          audioPlayer.playAudio();
        } else {
          loadRandomWhisper();
        }
        break;
      case 'browse':
        loadBrowseWhispers();
        break;
    }
  };

  const handleRecordToggle = () => {
    if (isRecording) {
      console.log('🛑 Record button pressed - stopping recording');
      audioRecorder.stopRecording();
    } else {
      console.log('🎙️ Record button pressed - starting recording');
      audioRecorder.startRecording();
    }
  };

  const renderContent = () => {
    switch (currentMode) {
      case 'record':
        return (
          <View style={styles.recordContent}>
            {isRecording && (
              <Text style={styles.recordingText}>
                Nahrávam: {audioRecorder.formatDuration(audioRecorder.recordingDuration)}
              </Text>
            )}
            <Text style={styles.hintText}>
              {isRecording ? 'Kliknite pre zastavenie' : 'Kliknite na kruh pre nahranie šepotu'}
            </Text>
          </View>
        );

      case 'listen':
        return (
          <View style={styles.listenContent}>
            {currentWhisper ? (
              <>
                {isPlaying && (
                  <View style={styles.progressContainer}>
                    <View style={styles.progressBar}>
                      <View
                        style={[
                          styles.progressFill,
                          { width: `${audioPlayer.getProgress() * 100}%` },
                        ]}
                      />
                    </View>
                    <Text style={styles.timeText}>
                      {audioPlayer.formatTime(audioPlayer.position)} / {audioPlayer.formatTime(audioPlayer.duration)}
                    </Text>
                  </View>
                )}
                <Text style={styles.hintText}>
                  {isPlaying ? 'Prehrávam anonymný šepot...' : 'Kliknite pre prehranie náhodného šepotu'}
                </Text>
              </>
            ) : (
              <Text style={styles.hintText}>
                {isLoading ? 'Hľadám šepot...' : 'Žiadne šepoty nie sú dostupné. Kliknite pre opakovaný pokus.'}
              </Text>
            )}
          </View>
        );

      case 'browse':
        return (
          <ScrollView style={styles.browseContent} showsVerticalScrollIndicator={false}>
            {browseWhispers.length > 0 ? (
              browseWhispers.map((whisper) => (
                <WhisperCard
                  key={whisper.id}
                  whisper={whisper}
                  onLike={handleLikeWhisper}
                  onReport={handleReportWhisper}
                />
              ))
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>
                  {isLoading ? 'Načítavam šepoty...' : 'Nenašli sa žiadne šepoty'}
                </Text>
              </View>
            )}
          </ScrollView>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ExpoStatusBar style="light" backgroundColor={colors.black} />

      <FloatingMenu
        currentMode={currentMode}
        onModeChange={setCurrentMode}
      />

      <View style={styles.mainContent}>
        {currentMode !== 'browse' && (
          <View style={styles.circleContainer}>
            <MainCircle
              mode={currentMode}
              onPress={currentMode === 'record' ? handleRecordToggle : handleMainCirclePress}
              isActive={isRecording || isPlaying}
              isRecording={isRecording}
              isPlaying={isPlaying}
            />
          </View>
        )}

        <View style={styles.contentContainer}>
          {renderContent()}
        </View>
      </View>

      {/* App Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.appTitle}>ŠEPOT</Text>
        <Text style={styles.appSubtitle}>👻 Anonymné zdieľanie zvuku</Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  mainContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    width: '100%',
    paddingHorizontal: spacing.lg,
  },
  recordContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  listenContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  browseContent: {
    flex: 1,
    paddingTop: spacing.xl,
  },
  recordingText: {
    color: colors.neonPurple,
    fontSize: fonts.size.large,
    fontWeight: 'bold',
    marginBottom: spacing.md,
  },
  hintText: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
    opacity: 0.8,
  },
  progressContainer: {
    width: '80%',
    marginBottom: spacing.lg,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.darkGray,
    borderRadius: 2,
    marginBottom: spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.neonPurple,
    borderRadius: 2,
  },
  timeText: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    textAlign: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyText: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
  },
  titleContainer: {
    position: 'absolute',
    bottom: 60,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  appTitle: {
    color: colors.neonPurple,
    fontSize: fonts.size.xxlarge,
    fontWeight: 'bold',
    letterSpacing: 4,
  },
  appSubtitle: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    marginTop: spacing.xs,
    opacity: 0.7,
  },
});
