import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { colors, fonts, spacing, borderRadius, shadows } from '../styles/theme';
import useAudioPlayer from './AudioPlayer';

const WhisperCard = ({ whisper, onLike, onReport }) => {
  const [scaleAnim] = useState(new Animated.Value(1));
  const [isLiked, setIsLiked] = useState(false);
  
  const audioPlayer = useAudioPlayer({
    audioUrl: whisper.audioUrl,
    onPlaybackStart: () => {
      // Visual feedback when playing starts
      Animated.spring(scaleAnim, {
        toValue: 1.05,
        useNativeDriver: false,
      }).start();
    },
    onPlaybackEnd: () => {
      // Return to normal size when playing ends
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: false,
      }).start();
    },
  });

  const handleLike = () => {
    if (!isLiked) {
      setIsLiked(true);
      onLike && onLike(whisper.id);
      
      // Like animation
      Animated.sequence([
        Animated.spring(scaleAnim, {
          toValue: 1.2,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const handleReport = () => {
    onReport && onReport(whisper.id);
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Neznáme';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Práve teraz';
    if (diffInHours < 24) return `pred ${diffInHours}h`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `pred ${diffInDays} dňami`;
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <View style={styles.card}>
        {/* Play Button */}
        <TouchableOpacity
          style={[
            styles.playButton,
            audioPlayer.isPlaying && styles.playButtonActive,
          ]}
          onPress={audioPlayer.playAudio}
          disabled={audioPlayer.isLoading}
        >
          <Text style={styles.playIcon}>
            {audioPlayer.isLoading ? '⏳' : audioPlayer.isPlaying ? '⏸️' : '▶️'}
          </Text>
        </TouchableOpacity>

        {/* Content */}
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.whisperText}>👻 Anonymný šepot</Text>
            <Text style={styles.timestamp}>
              {formatTimestamp(whisper.timestamp)}
            </Text>
          </View>

          {/* Progress Bar */}
          {audioPlayer.isPlaying && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${audioPlayer.getProgress() * 100}%` },
                  ]}
                />
              </View>
              <Text style={styles.timeText}>
                {audioPlayer.formatTime(audioPlayer.position)} / {audioPlayer.formatTime(audioPlayer.duration)}
              </Text>
            </View>
          )}

          {/* Actions */}
          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.actionButton, isLiked && styles.actionButtonLiked]}
              onPress={handleLike}
              disabled={isLiked}
            >
              <Text style={styles.actionIcon}>
                {isLiked ? '💚' : '🤍'}
              </Text>
              <Text style={styles.actionText}>
                {(whisper.likes || 0) + (isLiked ? 1 : 0)}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleReport}
            >
              <Text style={styles.actionIcon}>⚠️</Text>
              <Text style={styles.actionText}>Nahlásiť</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.sm,
    marginHorizontal: spacing.md,
  },
  card: {
    backgroundColor: colors.deepPurple,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.neonPurple,
    padding: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    ...shadows.dark,
  },
  playButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.neonPurple,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  playButtonActive: {
    backgroundColor: colors.white,
  },
  playIcon: {
    fontSize: 20,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  whisperText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
  },
  timestamp: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
  },
  progressContainer: {
    marginBottom: spacing.sm,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.darkGray,
    borderRadius: 2,
    marginBottom: spacing.xs,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.neonPurple,
    borderRadius: 2,
  },
  timeText: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.darkGray,
  },
  actionButtonLiked: {
    backgroundColor: colors.neonPurple,
  },
  actionIcon: {
    fontSize: 16,
    marginRight: spacing.xs,
  },
  actionText: {
    color: colors.white,
    fontSize: fonts.size.small,
  },
});

export default WhisperCard;
