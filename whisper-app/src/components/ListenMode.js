import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  PanResponder,
  Dimensions,
  TouchableOpacity,
  Vibration,
} from 'react-native';
import useAudioPlayer from './AudioPlayer';
import { colors, fonts, spacing, borderRadius, shadows } from '../styles/theme';

const { width } = Dimensions.get('window');
const SWIPE_THRESHOLD = width * 0.3; // 30% of screen width

const ListenMode = ({
  whisper,
  onSwipeLeft,
  onSwipeRight,
  canSwipeLeft,
  canSwipeRight,
  whisperIndex,
  totalWhispers,
  isLoading
}) => {
  const [swipeDirection, setSwipeDirection] = useState(null);
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const pan = useRef(new Animated.ValueXY()).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const scale = useRef(new Animated.Value(1)).current;
  const loadingDotAnim = useRef(new Animated.Value(0)).current;

  const audioPlayer = useAudioPlayer({
    audioUrl: whisper?.audioUrl,
    onPlaybackStart: () => {
      console.log('🎵 ListenMode: Audio playback started');
    },
    onPlaybackEnd: () => {
      console.log('🎵 ListenMode: Audio playback ended');
    },
  });

  // Reset animations when whisper changes
  useEffect(() => {
    pan.setValue({ x: 0, y: 0 });
    opacity.setValue(1);
    scale.setValue(1);
    setSwipeDirection(null);
    setIsSwipeActive(false);
  }, [whisper?.id]);

  // Loading animation
  useEffect(() => {
    if (isLoading) {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(loadingDotAnim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(loadingDotAnim, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      );
      animation.start();
      return () => animation.stop();
    }
  }, [isLoading]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // Only respond to horizontal swipes and not when audio is loading
        const isHorizontal = Math.abs(gestureState.dx) > Math.abs(gestureState.dy);
        const isSignificant = Math.abs(gestureState.dx) > 15;
        return isHorizontal && isSignificant && !audioPlayer.isLoading;
      },
      onPanResponderGrant: () => {
        setIsSwipeActive(true);
        // Light haptic feedback when starting swipe
        Vibration.vibrate(10);
        pan.setOffset({
          x: pan.x._value,
          y: pan.y._value,
        });
      },
      onPanResponderMove: (evt, gestureState) => {
        // Determine swipe direction and check if it's allowed
        let direction = null;
        let isAllowed = false;

        if (gestureState.dx > 0 && canSwipeRight) {
          direction = 'right';
          isAllowed = true;
        } else if (gestureState.dx < 0 && canSwipeLeft) {
          direction = 'left';
          isAllowed = true;
        }

        setSwipeDirection(direction);

        if (isAllowed) {
          // Apply movement with resistance for better feel
          const resistance = 0.7;
          pan.setValue({ x: gestureState.dx * resistance, y: 0 });
          
          // Update opacity and scale based on swipe distance
          const progress = Math.min(Math.abs(gestureState.dx) / SWIPE_THRESHOLD, 1);
          opacity.setValue(Math.max(0.4, 1 - progress * 0.6));
          scale.setValue(Math.max(0.9, 1 - progress * 0.1));
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        pan.flattenOffset();
        setIsSwipeActive(false);

        const shouldTriggerSwipe = Math.abs(gestureState.dx) > SWIPE_THRESHOLD;
        const direction = gestureState.dx > 0 ? 'right' : 'left';
        const isAllowed = (direction === 'right' && canSwipeRight) || (direction === 'left' && canSwipeLeft);

        if (shouldTriggerSwipe && isAllowed) {
          // Success haptic feedback
          Vibration.vibrate(50);

          // Animate out
          const toValue = gestureState.dx > 0 ? width : -width;

          Animated.parallel([
            Animated.timing(pan.x, {
              toValue,
              duration: 300,
              useNativeDriver: false,
            }),
            Animated.timing(opacity, {
              toValue: 0,
              duration: 300,
              useNativeDriver: false,
            }),
            Animated.timing(scale, {
              toValue: 0.8,
              duration: 300,
              useNativeDriver: false,
            }),
          ]).start(() => {
            // Trigger callback
            if (direction === 'right') {
              onSwipeRight && onSwipeRight();
            } else {
              onSwipeLeft && onSwipeLeft();
            }
          });
        } else {
          // Animate back to center
          Animated.parallel([
            Animated.spring(pan, {
              toValue: { x: 0, y: 0 },
              useNativeDriver: false,
              tension: 100,
              friction: 8,
            }),
            Animated.timing(opacity, {
              toValue: 1,
              duration: 200,
              useNativeDriver: false,
            }),
            Animated.timing(scale, {
              toValue: 1,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        }

        setSwipeDirection(null);
      },
    })
  ).current;

  const getSwipeIndicatorText = () => {
    if (swipeDirection === 'left') {
      return '👈 Ďalší šepot';
    } else if (swipeDirection === 'right') {
      return 'Predchádzajúci šepot 👉';
    }
    return '';
  };

  const getSwipeIndicatorStyle = () => {
    const baseStyle = [styles.swipeIndicator];
    if (swipeDirection === 'left') {
      return [...baseStyle, styles.swipeIndicatorLeft];
    } else if (swipeDirection === 'right') {
      return [...baseStyle, styles.swipeIndicatorRight];
    }
    return [...baseStyle, { opacity: 0 }];
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Načítavam šepot...</Text>
          <View style={styles.loadingDots}>
            <Animated.Text
              style={[
                styles.loadingDot,
                {
                  opacity: loadingDotAnim.interpolate({
                    inputRange: [0, 0.33, 0.66, 1],
                    outputRange: [0.3, 1, 0.3, 0.3],
                  })
                }
              ]}
            >
              ●
            </Animated.Text>
            <Animated.Text
              style={[
                styles.loadingDot,
                {
                  opacity: loadingDotAnim.interpolate({
                    inputRange: [0, 0.33, 0.66, 1],
                    outputRange: [0.3, 0.3, 1, 0.3],
                  })
                }
              ]}
            >
              ●
            </Animated.Text>
            <Animated.Text
              style={[
                styles.loadingDot,
                {
                  opacity: loadingDotAnim.interpolate({
                    inputRange: [0, 0.33, 0.66, 1],
                    outputRange: [0.3, 0.3, 0.3, 1],
                  })
                }
              ]}
            >
              ●
            </Animated.Text>
          </View>
        </View>
      </View>
    );
  }

  if (!whisper) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            Žiadne šepoty nie sú dostupné.
          </Text>
          <Text style={styles.emptySubtext}>
            Skúste neskôr alebo nahrajte vlastný šepot! 🎙️
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Swipe Indicators */}
      <Animated.View style={getSwipeIndicatorStyle()}>
        <Text style={styles.swipeIndicatorText}>
          {getSwipeIndicatorText()}
        </Text>
      </Animated.View>

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          {
            transform: [
              { translateX: pan.x },
              { scale: scale }
            ],
            opacity: opacity,
          },
        ]}
        {...panResponder.panHandlers}
      >
        {/* Play Button */}
        <TouchableOpacity
          style={[
            styles.playButton,
            audioPlayer.isPlaying && styles.playButtonActive,
            audioPlayer.isLoading && styles.playButtonLoading,
          ]}
          onPress={audioPlayer.playAudio}
          disabled={audioPlayer.isLoading}
        >
          <Text style={styles.playIcon}>
            {audioPlayer.isLoading ? '⏳' : audioPlayer.isPlaying ? '⏸️' : '▶️'}
          </Text>
        </TouchableOpacity>

        {/* Progress Bar */}
        {audioPlayer.isPlaying && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${audioPlayer.getProgress() * 100}%` },
                ]}
              />
            </View>
            <Text style={styles.timeText}>
              {audioPlayer.formatTime(audioPlayer.position)} / {audioPlayer.formatTime(audioPlayer.duration)}
            </Text>
          </View>
        )}

        {/* Status Text */}
        <Text style={styles.statusText}>
          {audioPlayer.isPlaying 
            ? 'Prehrávam anonymný šepot...' 
            : 'Kliknite pre prehranie šepotu'
          }
        </Text>

        {/* Whisper Counter */}
        {totalWhispers > 0 && (
          <View style={styles.counterContainer}>
            <Text style={styles.counterText}>
              {whisperIndex + 1} / {totalWhispers}
            </Text>
          </View>
        )}

        {/* Swipe Hints */}
        {!isSwipeActive && !audioPlayer.isPlaying && (
          <View style={styles.hintsContainer}>
            {canSwipeRight && (
              <View style={styles.hintItem}>
                <Text style={styles.hintText}>👉 Potiahnite pre predchádzajúci</Text>
              </View>
            )}
            {canSwipeLeft && (
              <View style={styles.hintItem}>
                <Text style={styles.hintText}>Potiahnite pre ďalší 👈</Text>
              </View>
            )}
          </View>
        )}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: spacing.lg,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    color: colors.lightGray,
    fontSize: fonts.size.large,
    marginBottom: spacing.md,
  },
  loadingDots: {
    flexDirection: 'row',
  },
  loadingDot: {
    color: colors.neonPurple,
    fontSize: fonts.size.large,
    marginHorizontal: spacing.xs,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyText: {
    color: colors.lightGray,
    fontSize: fonts.size.large,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  emptySubtext: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
    opacity: 0.7,
  },
  playButton: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.deepPurple,
    borderWidth: 3,
    borderColor: colors.neonPurple,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xl,
    ...shadows.glow,
    elevation: 8,
    shadowColor: colors.neonPurple,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  playButtonActive: {
    backgroundColor: colors.neonPurple,
    borderColor: colors.white,
    transform: [{ scale: 1.05 }],
    shadowOpacity: 0.5,
    shadowRadius: 12,
  },
  playButtonLoading: {
    opacity: 0.7,
    transform: [{ scale: 0.95 }],
  },
  playIcon: {
    fontSize: 40,
    color: colors.white,
  },
  progressContainer: {
    width: '80%',
    marginBottom: spacing.lg,
  },
  progressBar: {
    height: 6,
    backgroundColor: colors.darkGray,
    borderRadius: 3,
    marginBottom: spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.neonPurple,
    borderRadius: 3,
  },
  timeText: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    textAlign: 'center',
  },
  statusText: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
    marginBottom: spacing.lg,
    opacity: 0.8,
  },
  counterContainer: {
    marginBottom: spacing.lg,
  },
  counterText: {
    color: colors.neonPurple,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  hintsContainer: {
    position: 'absolute',
    bottom: spacing.xl,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
  },
  hintItem: {
    backgroundColor: colors.darkGray,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    opacity: 0.8,
  },
  hintText: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
  },
  swipeIndicator: {
    position: 'absolute',
    top: '40%',
    zIndex: 10,
    backgroundColor: colors.neonPurple,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    ...shadows.glow,
    elevation: 12,
    shadowColor: colors.neonPurple,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.6,
    shadowRadius: 15,
  },
  swipeIndicatorLeft: {
    right: spacing.lg,
  },
  swipeIndicatorRight: {
    left: spacing.lg,
  },
  swipeIndicatorText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
  },
});

export default ListenMode;
