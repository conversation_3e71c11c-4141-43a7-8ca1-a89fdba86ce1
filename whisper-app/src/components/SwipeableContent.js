import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  PanResponder,
  Dimensions,
} from 'react-native';
import { colors, fonts, spacing } from '../styles/theme';

const { width } = Dimensions.get('window');
const SWIPE_THRESHOLD = width * 0.25; // 25% of screen width

const SwipeableContent = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  canSwipeLeft = true,
  canSwipeRight = true,
  isLoading = false,
  showHints = true
}) => {
  const [swipeDirection, setSwipeDirection] = useState(null);
  const pan = useRef(new Animated.ValueXY()).current;
  const opacity = useRef(new Animated.Value(1)).current;

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // Only respond to horizontal swipes
        return Math.abs(gestureState.dx) > Math.abs(gestureState.dy) && Math.abs(gestureState.dx) > 10;
      },
      onPanResponderGrant: () => {
        pan.setOffset({
          x: pan.x._value,
          y: pan.y._value,
        });
      },
      onPanResponderMove: (evt, gestureState) => {
        // Determine swipe direction
        if (gestureState.dx > 0 && canSwipeRight) {
          setSwipeDirection('right');
        } else if (gestureState.dx < 0 && canSwipeLeft) {
          setSwipeDirection('left');
        } else {
          setSwipeDirection(null);
        }

        // Only allow movement if swipe is valid
        if ((gestureState.dx > 0 && canSwipeRight) || (gestureState.dx < 0 && canSwipeLeft)) {
          pan.setValue({ x: gestureState.dx, y: 0 });
          
          // Update opacity based on swipe distance
          const progress = Math.abs(gestureState.dx) / SWIPE_THRESHOLD;
          opacity.setValue(Math.max(0.3, 1 - progress * 0.7));
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        pan.flattenOffset();
        setSwipeDirection(null);

        const shouldTriggerSwipe = Math.abs(gestureState.dx) > SWIPE_THRESHOLD;

        if (shouldTriggerSwipe) {
          // Animate out
          const toValue = gestureState.dx > 0 ? width : -width;
          Animated.parallel([
            Animated.timing(pan.x, {
              toValue,
              duration: 200,
              useNativeDriver: false,
            }),
            Animated.timing(opacity, {
              toValue: 0,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start(() => {
            // Trigger callback
            if (gestureState.dx > 0 && canSwipeRight) {
              onSwipeRight && onSwipeRight();
            } else if (gestureState.dx < 0 && canSwipeLeft) {
              onSwipeLeft && onSwipeLeft();
            }

            // Reset position
            pan.setValue({ x: 0, y: 0 });
            opacity.setValue(1);
          });
        } else {
          // Animate back to center
          Animated.parallel([
            Animated.spring(pan, {
              toValue: { x: 0, y: 0 },
              useNativeDriver: false,
            }),
            Animated.timing(opacity, {
              toValue: 1,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        }
      },
    })
  ).current;

  const getSwipeIndicatorText = () => {
    if (swipeDirection === 'left') {
      return '👈 Ďalší šepot';
    } else if (swipeDirection === 'right') {
      return 'Predchádzajúci šepot 👉';
    }
    return '';
  };

  const getSwipeIndicatorStyle = () => {
    if (swipeDirection === 'left') {
      return [styles.swipeIndicator, styles.swipeIndicatorLeft];
    } else if (swipeDirection === 'right') {
      return [styles.swipeIndicator, styles.swipeIndicatorRight];
    }
    return [styles.swipeIndicator, { opacity: 0 }];
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Načítavam šepot...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Swipe Indicators */}
      <Animated.View style={getSwipeIndicatorStyle()}>
        <Text style={styles.swipeIndicatorText}>
          {getSwipeIndicatorText()}
        </Text>
      </Animated.View>

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          {
            transform: [{ translateX: pan.x }],
            opacity: opacity,
          },
        ]}
        {...panResponder.panHandlers}
      >
        {children}
      </Animated.View>

      {/* Swipe Hints */}
      {!swipeDirection && showHints && (
        <View style={styles.hintsContainer}>
          {canSwipeRight && (
            <Text style={styles.swipeHint}>👉 Potiahnite pre predchádzajúci</Text>
          )}
          {canSwipeLeft && (
            <Text style={styles.swipeHint}>Potiahnite pre ďalší 👈</Text>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
  },
  swipeIndicator: {
    position: 'absolute',
    top: '50%',
    zIndex: 10,
    backgroundColor: colors.neonPurple,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    transform: [{ translateY: -20 }],
  },
  swipeIndicatorLeft: {
    right: spacing.lg,
  },
  swipeIndicatorRight: {
    left: spacing.lg,
  },
  swipeIndicatorText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
  },
  hintsContainer: {
    position: 'absolute',
    bottom: spacing.xxl * 2, // Move higher to avoid app title
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
  },
  swipeHint: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    opacity: 0.8,
    backgroundColor: colors.darkGray,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
});

export default SwipeableContent;
