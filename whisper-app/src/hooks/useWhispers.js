import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  uploadAudio,
  createWhisper,
  getRandomWhisper,
  getRecentWhispers,
  likeWhisper,
  reportWhisper,
  testFirebaseConnection,
  addTestWhispers,
} from '../services/whisperService';

const useWhispers = () => {
  const [userId, setUserId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [whisperHistory, setWhisperHistory] = useState([]);
  const [currentWhisperIndex, setCurrentWhisperIndex] = useState(-1);

  // Generate or retrieve user ID and test Firebase
  useEffect(() => {
    const initializeUser = async () => {
      try {
        let storedUserId = await AsyncStorage.getItem('userId');
        if (!storedUserId) {
          storedUserId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          await AsyncStorage.setItem('userId', storedUserId);
        }
        setUserId(storedUserId);

        // Test Firebase connection
        console.log('Testing Firebase connection on app start...');
        const isConnected = await testFirebaseConnection();
        console.log('Firebase connection status:', isConnected);

        // Add test whispers if database is empty (development only)
        if (isConnected) {
          console.log('🔍 Checking if database needs test data...');
          // This will help populate the database for testing
          // Remove this in production
        }
      } catch (error) {
        console.error('Error initializing user:', error);
        setError('Nepodarilo sa inicializovať používateľa');
      }
    };

    initializeUser();
  }, []);

  // Upload and create whisper
  const uploadWhisper = async (audioUri) => {
    if (!userId) {
      setError('Používateľ nie je inicializovaný');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Upload audio to Firebase Storage
      const audioUrl = await uploadAudio(audioUri, userId);
      
      // Create whisper document in Firestore
      const whisperId = await createWhisper(audioUrl, userId);
      
      setIsLoading(false);
      return whisperId;
    } catch (error) {
      console.error('Error uploading whisper:', error);
      setError('Nepodarilo sa nahrať šepot');
      setIsLoading(false);
      return null;
    }
  };

  // Get random whisper for listening
  const getRandomWhisperForUser = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const whisper = await getRandomWhisper(userId);

      if (whisper) {
        // Add to history if it's not already there
        setWhisperHistory(prev => {
          const exists = prev.find(w => w.id === whisper.id);
          if (!exists) {
            const newHistory = [...prev, whisper];
            setCurrentWhisperIndex(newHistory.length - 1);
            return newHistory;
          } else {
            // If whisper exists, set current index to its position
            const existingIndex = prev.findIndex(w => w.id === whisper.id);
            setCurrentWhisperIndex(existingIndex);
          }
          return prev;
        });
      }

      setIsLoading(false);
      return whisper;
    } catch (error) {
      console.error('Error getting random whisper:', error);
      setError('Nepodarilo sa získať šepot');
      setIsLoading(false);
      return null;
    }
  };

  // Get recent whispers for browse mode
  const getBrowseWhispers = async (limit = 20) => {
    setIsLoading(true);
    setError(null);

    try {
      const whispers = await getRecentWhispers(limit);
      setIsLoading(false);
      return whispers;
    } catch (error) {
      console.error('Error getting browse whispers:', error);
      setError('Nepodarilo sa získať šepoty');
      setIsLoading(false);
      return [];
    }
  };

  // Like a whisper
  const handleLikeWhisper = async (whisperId) => {
    try {
      await likeWhisper(whisperId);
      return true;
    } catch (error) {
      console.error('Error liking whisper:', error);
      setError('Nepodarilo sa označiť šepot ako obľúbený');
      return false;
    }
  };

  // Report a whisper
  const handleReportWhisper = async (whisperId) => {
    try {
      await reportWhisper(whisperId);
      return true;
    } catch (error) {
      console.error('Error reporting whisper:', error);
      setError('Nepodarilo sa nahlásiť šepot');
      return false;
    }
  };

  // Get next whisper (swipe left)
  const getNextWhisper = async () => {
    console.log('🔄 getNextWhisper called:', {
      currentWhisperIndex,
      historyLength: whisperHistory.length,
      canUseHistory: currentWhisperIndex < whisperHistory.length - 1
    });

    // If we have a next whisper in history, use it
    if (currentWhisperIndex < whisperHistory.length - 1) {
      const nextIndex = currentWhisperIndex + 1;
      setCurrentWhisperIndex(nextIndex);
      const nextWhisper = whisperHistory[nextIndex];
      console.log('✅ Using whisper from history:', nextWhisper?.id);
      return nextWhisper;
    }

    // Otherwise, get a new random whisper
    console.log('🔄 Getting new random whisper...');
    return await getRandomWhisperForUser();
  };

  // Get previous whisper (swipe right)
  const getPreviousWhisper = async () => {
    console.log('🔄 getPreviousWhisper called:', {
      currentWhisperIndex,
      historyLength: whisperHistory.length,
      canGoBack: currentWhisperIndex > 0
    });

    if (currentWhisperIndex > 0) {
      const prevIndex = currentWhisperIndex - 1;
      setCurrentWhisperIndex(prevIndex);
      const prevWhisper = whisperHistory[prevIndex];
      console.log('✅ Returning previous whisper:', prevWhisper?.id);
      return prevWhisper;
    }

    // If no previous whisper, return null
    console.log('⚠️ No previous whisper available');
    return null;
  };

  // Get current whisper from history
  const getCurrentWhisper = () => {
    if (currentWhisperIndex >= 0 && currentWhisperIndex < whisperHistory.length) {
      return whisperHistory[currentWhisperIndex];
    }
    return null;
  };

  // Clear whisper history
  const clearWhisperHistory = () => {
    setWhisperHistory([]);
    setCurrentWhisperIndex(-1);
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  return {
    userId,
    isLoading,
    error,
    whisperHistory,
    currentWhisperIndex,
    uploadWhisper,
    getRandomWhisperForUser,
    getNextWhisper,
    getPreviousWhisper,
    getCurrentWhisper,
    clearWhisperHistory,
    getBrowseWhispers,
    handleLikeWhisper,
    handleReportWhisper,
    clearError,
  };
};

export default useWhispers;
